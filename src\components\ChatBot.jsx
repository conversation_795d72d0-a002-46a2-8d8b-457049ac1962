import React, { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import '@fortawesome/fontawesome-free/css/all.min.css';
 
const ChatBot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: " Hi there, I’m an AI Agent from Makonis, if you have any questions just let me know. Saw that you’re interested in our products/services. I’m available if you have any questions or need help.",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const chatPopupRef = useRef(null);
  const chatIconRef = useRef(null);
  const messagesEndRef = useRef(null);
 
  useEffect(() => {
    if (isOpen && chatPopupRef.current) {
      gsap.fromTo(chatPopupRef.current,
        { opacity: 0, scale: 0.8, y: 20 },
        { opacity: 1, scale: 1, y: 0, duration: 0.3, ease: "back.out(1.7)" }
      );
    }
  }, [isOpen]);

  useEffect(() => { 
    Hi there, I’m an AI Agent from Makonis, if you have any questions just let me know. Saw that you’re interested in our products/services. I’m available if you have any questions or need help.zz
  ),[]}

    if (chatIconRef.current) {
      gsap.to(chatIconRef.current, {
        scale: 1.1,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });
    }
  }, []);
 
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
 
  useEffect(() => {
    if (chatIconRef.current) {
      gsap.to(chatIconRef.current, {
        scale: 1.1,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });
    }
  }, []);
 
  const toggleChat = () => {
    setIsOpen(!isOpen);
  };
 
  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      const newMessage = {
        id: messages.length + 1,
        text: inputMessage,
        sender: 'user',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, newMessage]);
      setInputMessage('');
 
      setTimeout(() => {
        const botResponse = {
          id: messages.length + 2,
          text: getBotResponse(inputMessage),
          sender: 'bot',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, botResponse]);
      }, 1000);
    }
  };
 
  const handleQuickReply = (text) => {
    setInputMessage(text);
    handleSendMessage();
  };
 
  const getBotResponse = () => {
    return "Thank you for your message. We will get back to you soon.";
  };
 
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };
 
  return (
    <>
      {isOpen && (
        <div
          ref={chatPopupRef}
          className="position-fixed"
          style={{
            bottom: '100px',
            right: '20px',
            width: '350px',
            height: '500px',
            background: '#f6f9fc',
            borderRadius: '20px',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',
            zIndex: 9998,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Header */}
          <div style={{ position: 'relative' }}>
            <div
              className="text-white"
              style={{
                background: 'linear-gradient(135deg, #002244 0%, #0066cc 100%)',
                borderTopLeftRadius: '20px',
                borderTopRightRadius: '20px',
                padding: '16px'
              }}
            >
              <div className="d-flex align-items-center mb-1">
                <div style={{
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  background: '#fff',
                  marginRight: '10px'
                }}></div>
                <div style={{ flexGrow: 1 }}>
                  <div style={{ fontSize: '14px' }}>Chat with</div>
                  <div style={{ fontWeight: 'bold' }}>Makonis Assistant</div>
                </div>
                <div onClick={toggleChat} style={{ cursor: 'pointer' }}>
                  <i className="fas fa-chevron-down text-white"></i>
                </div>
              </div>
              <div style={{ fontSize: '12px', marginLeft: '2px' }}>We are online!</div>
            </div>
 
            {/* Wave */}
            <svg viewBox="0 0 500 50" preserveAspectRatio="none" style={{ display: 'block', width: '100%', height: '30px' }}>
              <path
                d="M0,20 C150,60 350,0 500,30 L500,00 L0,0 Z"
                style={{ fill: 'url(#gradient)' }}
              ></path>
              <defs>
                <linearGradient id="gradient" x1="0" x2="1" y1="0" y2="0">
                  <stop offset="0%" stopColor="#002244" />
                  <stop offset="100%" stopColor="#0066cc" />
                </linearGradient>
              </defs>
            </svg>
          </div>
 
          {/* Messages */}
          <div className="flex-grow-1 p-3" style={{ overflowY: 'auto' }}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`mb-3 d-flex ${message.sender === 'user' ? 'justify-content-end' : 'justify-content-start'}`}
              >
                <div
                  className="px-3 py-2"
                  style={{
                    maxWidth: '80%',
                    borderRadius: '20px',
                    background: message.sender === 'user'
                      ? 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)'
                      : '#e0e7ef',
                    color: message.sender === 'user' ? '#fff' : '#333',
                    fontSize: '0.9rem'
                  }}
                >
                  {message.text}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
 
          {/* Quick Replies */}
          <div className="px-3 pb-2 d-flex gap-2">
            {['Yes', 'No', 'Help'].map((text, idx) => (
              <button
                key={idx}
                className="btn btn-outline-primary btn-sm"
                style={{
                  borderRadius: '15px',
                  fontSize: '0.8rem',
                  padding: '5px 10px'
                }}
                onClick={() => handleQuickReply(text)}
              >
                {text}
              </button>
            ))}
          </div>
 
          {/* Input */}
          <div className="p-3" style={{ borderTop: '1px solid #eee' }}>
            <div className="d-flex align-items-center">
              <input
                type="text"
                placeholder="Enter your message..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                style={{
                  flex: 1,
                  border: 'none',
                  outline: 'none',
                  padding: '10px',
                  fontSize: '0.9rem',
                  background: 'transparent'
                }}
              />
              <i
                className="fab fa-telegram-plane"
                style={{
                  color: '#0066cc',
                  fontSize: '2.2rem',
                  cursor: 'pointer',
                  marginLeft: '8px'
                }}
                onClick={handleSendMessage}
              />
            </div>
          </div>
        </div>
      )}
 
      {/* Chat Icon */}
      <div
        ref={chatIconRef}
        onClick={toggleChat}
        className="position-fixed d-flex align-items-center justify-content-center"
        style={{
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
          borderRadius: '50%',
          cursor: 'pointer',
          zIndex: 9999,
          boxShadow: '0 8px 25px rgba(0, 160, 233, 0.4)',
          border: '3px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <i className={`fas ${isOpen ? 'fa-times' : 'fa-comments'} text-white`} style={{ fontSize: '1.5rem' }} />
      </div>
    </>
  );
};
 
export default ChatBot;