import React, { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import About1 from "../Asserts/About1.png";
import About2 from "../Asserts/About2.png";
import About3 from "../Asserts/About3.png";
import About4 from "../Asserts/About4.png";

gsap.registerPlugin(ScrollTrigger);

// 3D Particle System Component
const ParticleSystem = () => {
  const canvasRef = useRef(null);
  const particlesRef = useRef([]);
  const mouseRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationId;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle class
    class Particle {
      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.z = Math.random() * 1000;
        this.vx = (Math.random() - 0.5) * 0.5;
        this.vy = (Math.random() - 0.5) * 0.5;
        this.vz = (Math.random() - 0.5) * 2;
        this.size = Math.random() * 3 + 1;
        this.opacity = Math.random() * 0.8 + 0.2;
        this.hue = Math.random() * 60 + 200; // Blue to cyan range
      }

      update() {
        this.x += this.vx;
        this.y += this.vy;
        this.z += this.vz;

        // Mouse interaction
        const dx = mouseRef.current.x - this.x;
        const dy = mouseRef.current.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          const force = (100 - distance) / 100;
          this.x -= dx * force * 0.01;
          this.y -= dy * force * 0.01;
        }

        // Wrap around edges
        if (this.x < 0) this.x = canvas.width;
        if (this.x > canvas.width) this.x = 0;
        if (this.y < 0) this.y = canvas.height;
        if (this.y > canvas.height) this.y = 0;
        if (this.z < 0) this.z = 1000;
        if (this.z > 1000) this.z = 0;
      }

      draw() {
        const scale = 1000 / (1000 - this.z);
        const x2d = this.x * scale;
        const y2d = this.y * scale;
        const size2d = this.size * scale;

        ctx.save();
        ctx.globalAlpha = this.opacity * (1 - this.z / 1000);
        ctx.fillStyle = `hsl(${this.hue}, 70%, 60%)`;
        ctx.shadowBlur = 10;
        ctx.shadowColor = `hsl(${this.hue}, 70%, 60%)`;
        ctx.beginPath();
        ctx.arc(x2d, y2d, size2d, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      }
    }

    // Initialize particles
    for (let i = 0; i < 150; i++) {
      particlesRef.current.push(new Particle());
    }

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particlesRef.current.forEach(particle => {
        particle.update();
        particle.draw();
      });

      // Draw connections
      ctx.strokeStyle = 'rgba(0, 160, 233, 0.1)';
      ctx.lineWidth = 1;
      for (let i = 0; i < particlesRef.current.length; i++) {
        for (let j = i + 1; j < particlesRef.current.length; j++) {
          const dx = particlesRef.current[i].x - particlesRef.current[j].x;
          const dy = particlesRef.current[i].y - particlesRef.current[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 80) {
            ctx.globalAlpha = (80 - distance) / 80 * 0.3;
            ctx.beginPath();
            ctx.moveTo(particlesRef.current[i].x, particlesRef.current[i].y);
            ctx.lineTo(particlesRef.current[j].x, particlesRef.current[j].y);
            ctx.stroke();
          }
        }
      }

      animationId = requestAnimationFrame(animate);
    };

    animate();

    // Mouse tracking
    const handleMouseMove = (e) => {
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
    };
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
      cancelAnimationFrame(animationId);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

// Morphing Text Component
const MorphingText = ({ texts, className, style }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const textRef = useRef(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % texts.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [texts.length]);

  useEffect(() => {
    if (textRef.current) {
      gsap.fromTo(textRef.current,
        { opacity: 0, y: 20, scale: 0.9 },
        { opacity: 1, y: 0, scale: 1, duration: 0.8, ease: "power2.out" }
      );
    }
  }, [currentIndex]);

  return (
    <span ref={textRef} className={className} style={style}>
      {texts[currentIndex]}
    </span>
  );
};

// Enhanced SVG Blob for modern feel
const ModernBlob1 = () => (
  <svg
    width="500"
    height="500"
    viewBox="0 0 200 200"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      zIndex: 0,
      opacity: 0.08,
      position: "absolute",
      top: "-100px",
      right: "-150px",
    }}
  >
    <defs>
      <linearGradient
        id="aboutBlobGradient1"
        x1="0%"
        y1="0%"
        x2="100%"
        y2="100%"
      >
        <stop offset="0%" style={{ stopColor: "#007bff", stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: "#00a0e9", stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path
      fill="url(#aboutBlobGradient1)"
      d="M60.1,-66.9C76.5,-56.8,87.5,-37.9,89.9,-18.3C92.3,1.3,86.1,21.5,74.1,37.9C62.1,54.3,44.3,67,25.5,73.7C6.7,80.4,-13.2,81.2,-30.9,74.8C-48.6,68.4,-64.1,54.8,-72.4,38.2C-80.7,21.6,-81.8,2,-76.5,-16.1C-71.2,-34.2,-59.5,-50.8,-44.4,-61C-29.3,-71.1,-10.8,-74.7,9.3,-77.2C29.4,-79.7,51.1,-82.3,60.1,-66.9Z"
      transform="translate(100 100) scale(1.1)"
    />
  </svg>
);

const ModernBlob2 = () => (
  <svg
    width="400"
    height="400"
    viewBox="0 0 200 200"
    xmlns="http://www.w3.org/2000/svg"
    style={{
      zIndex: 0,
      opacity: 0.07,
      position: "absolute",
      bottom: "-120px",
      left: "-100px",
    }}
  >
    <defs>
      <linearGradient
        id="aboutBlobGradient2"
        x1="0%"
        y1="0%"
        x2="100%"
        y2="100%"
      >
        <stop offset="0%" style={{ stopColor: "#6f42c1", stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: "#00a0e9", stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path
      fill="url(#aboutBlobGradient2)"
      d="M50.9,-57.9C65.9,-47.7,78.1,-33.5,81.2,-17.2C84.3,-0.9,78.3,17.6,67.3,32.8C56.3,48,40.3,59.9,23.3,66.6C6.2,73.3,-11.9,74.8,-28.7,69.8C-45.5,64.8,-61,53.3,-69.5,38.3C-77.9,23.3,-79.3,4.8,-74.7,-12.6C-70.1,-30,-59.5,-46.3,-45.7,-56.6C-31.9,-66.9,-14.9,-71.2,2.4,-73.2C19.7,-75.2,35.9,-68.1,50.9,-57.9Z"
      transform="translate(100 100) scale(0.9)"
    />
  </svg>
);

// --- Data and Components for New Layout ---

// 1. Data for the "Our Philosophy" section
const philosophyPoints = [
  {
    icon: "fas fa-bullseye",
    title: "Redefining Quality",
    description:
      "We are setting a new standard for what quality means in a digital-first world through precision and excellence.",
  },
  {
    icon: "fas fa-cogs",
    title: "Empowering Evolution",
    description:
      "We guide businesses on their technological journey, from the initial vision to successful, scalable execution.",
  },
  {
    icon: "fas fa-shield-alt",
    title: "Responsible Innovation",
    description:
      "We embrace cutting-edge tech with a realistic mindset, ensuring sustainable and positive impact for all stakeholders.",
  },
];

// 2. Component for "Our Philosophy" section
const OurPhilosophySection = ({ philosophyRef }) => (
  <div ref={philosophyRef} className="text-center mb-20 lg:mb-32">
    <h2
      style={{
        fontSize: "3rem",
        fontWeight: "700",
        letterSpacing: "2px",
        marginBottom: "1rem",
        background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
      }}
    >
      Our Philosophy
    </h2>
    <p className="text-xl text-white/80 max-w-3xl mx-auto mb-16">
      Our approach is built on three core pillars that drive success and
      sustainable growth.
    </p>
    <div className="flex flex-col md:flex-row justify-center gap-8 lg:gap-12">
      {philosophyPoints.map((point, index) => (
        <div
          key={index}
          className="flex-1 text-center card-makonis-glass p-6 rounded-2xl"
          style={{
            background: "rgba(255, 255, 255, 0.05)",
            backdropFilter: "blur(15px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <div className="w-16 h-16 bg-makonis-gradient rounded-full flex items-center justify-center mx-auto mb-4 shadow-glow">
            <i className={`${point.icon} text-white text-2xl`}></i>
          </div>
          <h3 className="text-xl font-bold text-white mb-3">{point.title}</h3>
          <p className="text-white/80 leading-relaxed">{point.description}</p>
        </div>
      ))}
    </div>
  </div>
);

// 3. Component for "Core Principles" (replaces values grid)
const CorePrinciplesSection = ({ valuesRef, coreValues }) => (
  <div className="space-y-20 lg:space-y-24">
    {coreValues.map((value, index) => (
      <div
        key={index}
        ref={(el) => (valuesRef.current[index] = el)}
        className={`flex flex-col md:flex-row items-center gap-8 lg:gap-12 ${
          index % 2 === 0 ? "" : "md:flex-row-reverse"
        }`}
      >
        <div className="md:w-1/2 text-center md:text-left">
          <div className="inline-block">
            <div className="w-16 h-16 bg-makonis-gradient rounded-2xl flex items-center justify-center mb-4 shadow-glow">
              <i className={`${value.icon} text-white text-2xl`}></i>
            </div>
          </div>
          <h3 className="text-3xl font-bold text-white mb-4">{value.title}</h3>
          <p className="text-lg text-white/80 leading-relaxed">
            {value.description}
          </p>
        </div>
        <div
          // Added max-w-xs to control the maximum width of the image container
          className="md:w-1/2 flex items-center justify-center card-makonis-glass rounded-2xl p-4 max-w-xs md:max-w-md lg:max-w-lg mx-auto"
          style={{
            background: "rgba(255, 255, 255, 0.05)",
            backdropFilter: "blur(15px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            aspectRatio: '16 / 9', // Set a fixed aspect ratio for consistency
            overflow: 'hidden', // Hide overflow if object-cover crops
          }}
        >
          <img
            src={value.image}
            alt={value.title}
            className="w-full h-full object-cover rounded-lg" // Changed to object-cover to fill the container
          />
        </div>
      </div>
    ))}
  </div>
);

// --- Main About Us Page Component ---

const AboutUsPage = () => {
  const sectionRef = useRef(null);
  const heroRef = useRef(null);
  const philosophyRef = useRef(null);
  const valuesRef = useRef([]);
  const backgroundRef = useRef(null);
  const hologramRef = useRef(null);

  // Interactive states
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHologramActive, setIsHologramActive] = useState(false);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);

  useEffect(() => {
    valuesRef.current = [];

    const ctx = gsap.context(() => {
      // Hero section animation
      gsap.from(heroRef.current.children, {
        y: 80,
        opacity: 0,
        duration: 1.2,
        stagger: 0.2,
        ease: "power2.out",
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      });

      // Philosophy section animation
      gsap.from(philosophyRef.current.children, {
        y: 60,
        opacity: 0,
        duration: 1,
        stagger: 0.2,
        ease: "power2.out",
        scrollTrigger: {
          trigger: philosophyRef.current,
          start: "top 75%",
          toggleActions: "play none none reverse",
        },
      }); // Core Principles animation

      valuesRef.current.forEach((card) => {
        gsap.from(card, {
          y: 50,
          opacity: 0,
          duration: 0.8,
          ease: "power2.out",
          scrollTrigger: {
            trigger: card,
            start: "top 85%",
            toggleActions: "play none none reverse",
          },
        });
      }); // Background parallax effect

      gsap.to(backgroundRef.current, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true,
        },
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const coreValues = [
    {
      icon: "fas fa-lightbulb",
      image:About1,
      title: "Innovation",
      description:
        "We embrace cutting-edge technology while staying realistic about its challenges and practical applications.",
    },
    {
      icon: "fas fa-handshake",
      image:About2,
      title: "Responsibility",
      description:
        "We operate with a responsible mindset, ensuring sustainable impact for our clients and communities.",
    },
    {
      icon: "fas fa-rocket",
      image:About3,
      title: "Evolution",
      description:
        "We empower businesses to evolve through technology, from initial vision to successful execution.",
    },
    {
      icon: "fas fa-gem",
      image:About4,
      title: "Quality",
      description:
        "We are redefining what quality means in a digital-first world through excellence and precision.",
    },
  ];

  return (
    <div
      ref={sectionRef}
      className="min-h-screen relative overflow-hidden"
      style={{
        background:
          "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {" "}
      <div ref={backgroundRef} className="absolute inset-0 z-0">
        {" "}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
        {/* NOTE: The lord-icon elements require you to include the Lordicon script in your project's index.html */}{" "}
        {[
          {
            lordicon: "https://cdn.lordicon.com/qhviklyi.json",
            top: "10%",
            left: "5%",
            delay: 0,
          },
          {
            lordicon: "https://cdn.lordicon.com/kiynvdns.json",
            top: "20%",
            right: "8%",
            delay: 1,
          },
          {
            lordicon: "https://cdn.lordicon.com/hwjcdycb.json",
            bottom: "15%",
            left: "3%",
            delay: 2,
          },
          {
            lordicon: "https://cdn.lordicon.com/qhgmphtg.json",
            bottom: "25%",
            right: "5%",
            delay: 3,
          },
        ].map((item, index) => (
          <div
            key={index}
            className="absolute w-15 h-15 bg-makonis-secondary/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-makonis-secondary/20 animate-float"
            style={{ ...item, animationDelay: `${item.delay}s` }}
          >
            {" "}
            <lord-icon
              src={item.lordicon}
              trigger="hover"
              colors="primary:#00a0e9"
              style={{ width: "24px", height: "24px" }}
            />{" "}
          </div>
        ))}
        <ModernBlob1 />
        <ModernBlob2 />{" "}
      </div>{" "}
      <div className="container-makonis relative z-10 section-padding py-20 lg:py-32">
        {" "}
        <div ref={heroRef} className="text-center mb-20 lg:mb-32">
          {" "}
          <h1
            style={{
              fontSize: "4rem",
              fontWeight: "800",
              letterSpacing: "3px",
              marginBottom: "2rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            About Makonis{" "}
          </h1>{" "}
          <div className="w-32 h-1 mx-auto mb-8 relative">
            {" "}
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background:
                  "linear-gradient(90deg, transparent, #00a0e9, transparent)",
              }}
            />{" "}
          </div>{" "}
          <p className="text-xl text-white/90 leading-relaxed mx-auto max-w-4xl">
            Makonis is a leading software solutions provider, dedicated to
            transforming businesses through cutting-edge technology. With a
            focus on innovation, responsibility, and quality, we empower
            organizations to achieve their digital transformation goals.{" "}
          </p>{" "}
        </div>
        <OurPhilosophySection philosophyRef={philosophyRef} />
        <div className="text-center mb-16 lg:mb-20">
          <h2
            style={{
              fontSize: "3rem",
              fontWeight: "700",
              letterSpacing: "2px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            Our Core Principles
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            The principles that guide our approach to technology and business
            transformation.
          </p>
        </div>
        <CorePrinciplesSection valuesRef={valuesRef} coreValues={coreValues} />{" "}
      </div>{" "}
      <style>{`
        /* NOTE: The .fa classes require you to have Font Awesome included in your project */
        .card-makonis-glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
        }
        .bg-makonis-gradient {
            background: linear-gradient(135deg, #007bff 0%, #00a0e9 100%);
        }
        .shadow-glow {
            box-shadow: 0 0 20px rgba(0, 160, 233, 0.4);
        }
@keyframes float { 
0%, 100% { transform: translateY(0px) rotate(0deg); } 
33% { transform: translateY(-15px) rotate(120deg); } 
66% { transform: translateY(5px) rotate(240deg); } 
}
.animate-float {
animation: float 6s ease-in-out infinite;
}
`}</style>{" "}
    </div>
  );
};

export default AboutUsPage;