import { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Image, ListGroup } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";
import staffAugumentation1 from "../Asserts/staffAugmentation1.jpg";
import staffAugumentation3 from "../Asserts/staffAugmentation3.png";
import staffAugumentation2 from "../Asserts/staffAugumentation2.png";

gsap.registerPlugin(ScrollTrigger);

const staffAugmentationData = {
  hero: {
    title: "Staff Augmentation Services",
    subtitle:
      "Staff Augmentation is one of the most useful types of staffing models there is. With a dramatic increase in demand for temporary and contingent staff across organizations globally, especially in IT companies, IT staff augmentation services are one of the most sought-after services.",
    backgroundImage:
      "https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80",
  },
  sections: [
    {
      id: "why-choose",
      title: "Why Choose Our Staff Augmentation Services?",
      texts: [""],
      listItems: [
        "Access to Skilled Candidates: We provide you with a single point of contact, dedicated staffing expert who understands your unique requirements and ensures you acquire talented, experienced candidates with specialized skills.",
        "Agility and Flexibility: Our expert staff work swiftly to equip your team with candidates with specialized skills for immediate skilled candidate requirements.",
        "Pay Per Service Model: You only pay for the staff augmentation services you receive from us. We provide contractual, on-demand candidates as and when you require.",
        "Contract Based Service: The client has full rights to dictate the terms of the contract. We encourage our clients to define the contracts and NDAs for the selected employees.",
        // "**End-to-end Staffing Support:** Our specialized HR team handles every aspect related to HR management including on-demand supply, profiling, selection, onboarding and candidate care management.",
        // "**Scalable Model:** We offer staff augmentation services that are scalable, allowing you the flexibility to scale down your workforce as per your business needs."
      ],
      image:
        staffAugumentation1,
      alt: "Staff augmentation services and team collaboration",
      reversed: false,
    },
    {
      id: "types",
      title: "Staff Augmentation Solutions We Provide",
      texts: [""],
      listItems: [
        "Skill-Based: We provide organizations with IT staff augmentation services for individuals with specific technical skillsets like Cybersecurity experts, graphic designers, UX/UI designers, digital marketing specialists.",
        "Project Based: If an organization needs an employee with specific tech skills to work for a specific period on IT projects like software development, project design, etc.",
        "Temporary/Seasonal: We can provide temporary staff who can swiftly integrate into your team during seasons when there is a spike in workload.",
        "Long Term Staff Augmentation: We deploy staff to work on a long-term basis as part of your in-house team for an indefinite period.",
        // "**Industry Specific:** We cater to specific industries that require individuals with specific skills including nurses, healthcare professionals, accountants, risk managers.",
        // "**IT Staff Augmentation:** We help you find candidates with specialized technical skillsets for technical job roles including AI/ML specialists, Blockchain developers, legal compliance consultants."
      ],
      image:
        staffAugumentation2,
      alt: "Different types of staff augmentation services",
      reversed: true,
    },
    {
      id: "value-proposition",
      title: "Our Value Proposition",
      texts: [""],
      listItems: [
        "Cost-efficient: Staff-augmented services that help you maintain a high ROI",
        "Comprehensive Evaluation: We evaluate the core requirements of the client, devise a roadmap, and deploy full-fledged staff-augmented services",
        "Quality Selection: Select candidates with the required skills, who are certified, talented and experts in their field",
        "Streamlined Documentation: Spearhead the documentation process involving recruiting and on-boarding formalities to avoid any delayed start",
        // "**Post-deployment Support:** Provide support post-deployment, to maintain efficiency, and productivity throughout the project"
      ],
      image:
        staffAugumentation3,
      alt: "Value proposition and benefits of staff augmentation",
      reversed: false,
    },
  ],
};

const StaffAugmentationPage = () => {
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animation
      gsap.from(heroRef.current.querySelector("h1"), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      });

      gsap.from(heroRef.current.querySelector("p"), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3,
      });

      gsap.from(heroRef.current.querySelector(".btn"), {
        y: 30,
        opacity: 0,
        duration: 0.8,
        ease: "back.out(1.7)",
        delay: 0.6,
      });

      // Sections animations
      sectionsRef.current.forEach((section) => {
        if (section) {
          gsap.from(section.querySelector("h2"), {
            y: 50,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });

          gsap.from(section.querySelectorAll("p"), {
            y: 30,
            opacity: 0,
            duration: 0.8,
            stagger: 0.2,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 75%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });

          gsap.from(section.querySelector("img"), {
            scale: 0.8,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 70%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });
        }
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const [hoveredImage, setHoveredImage] = useState(null);

  // Reusable Style Objects
  const primaryColor = "#007bff";
  const primaryDarkColor = "#0056b3";
  const primaryRgb = "0,123,255";

  const ctaButtonBaseStyle = {
    padding: "1.2rem 3rem",
    fontSize: "1.2rem",
    background: `linear-gradient(95deg, ${primaryColor}, ${primaryDarkColor})`,
    border: "none",
    borderRadius: "50px",
    boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
    transition: "all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)",
    transform: "translateY(0)",
    color: "#fff",
    textDecoration: "none",
    display: "inline-block",
  };

  const featureImageContainerStyle = (isHovered) => ({
    borderRadius: "1.25rem",
    overflow: "hidden",
    boxShadow: isHovered
      ? "0 1.25rem 3.5rem rgba(0,0,0,0.2)"
      : "0 0.75rem 2rem rgba(0,0,0,0.1)",
    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
    transform: isHovered
      ? "scale(1.04) translateY(-8px)"
      : "scale(1) translateY(0)",
    backgroundColor: "#f0f2f5",
  });

  const featureImageStyle = {
    width: "100%",
    height: "100%",
    minHeight: "400px",
    objectFit: "cover",
    transition: "transform 0.6s ease",
  };

  return (
    <div className="staff-augmentation-page-wrapper">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="staff-augmentation-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${staffAugmentationData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Staff Icons */}
          {[
            { icon: "fa-users", top: "15%", left: "10%", delay: 0 },
            { icon: "fa-laptop-code", top: "25%", right: "15%", delay: 1 },
            { icon: "fa-project-diagram", bottom: "20%", left: "8%", delay: 2 },
            { icon: "fa-user-plus", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  color: "rgba(0, 160, 233, 0.8)",
                  fontSize: "1.5rem",
                }}
              />
            </div>
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: "300px",
              height: "300px",
              background:
                "radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)",
              borderRadius: "50%",
              top: "20%",
              right: "10%",
              filter: "blur(40px)",
              animation: "pulse 4s ease-in-out infinite",
            }}
          />
        </div>
        <Container className="position-relative z-index-1">
          <h1
            style={{
              fontSize: "5rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            {staffAugmentationData.hero.title}
          </h1>
          <p
            className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "1.35rem",
            }}
          >
            {staffAugmentationData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Feature Sections */}
      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {staffAugmentationData.sections.map((section, idx) => (
            <section
              key={section.id}
              ref={(el) => (sectionsRef.current[idx] = el)}
              className="mb-5 mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row className="mb-4">
                <Col xs={12}>
                  <h2
                    className="display-5 fw-bold text-center"
                    style={{
                      fontSize: "3.6rem",
                      fontWeight: "800",
                      letterSpacing: "2.6px",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {section.title}
                  </h2>
                </Col>
              </Row>

              {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative mb-5">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  section.reversed ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={10}
                  className={`${section.reversed ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    {section.texts.map((text, textIdx) => (
                      <p
                        key={textIdx}
                        className="mb-3 mb-md-4"
                        style={{
                          fontSize: "1.3rem",
                          lineHeight: "1.7",
                          color: "rgba(255, 255, 255, 0.9)",
                          textAlign: "justify",
                        }}
                      >
                        {text}
                      </p>
                    ))}
                    {section.listItems && (
                      <div className="mt-3 mt-md-4 mb-3 mb-md-4">
                        {section.listItems.map((item, itemIdx) => (
                          <div
                            key={itemIdx}
                            className="d-flex align-items-start mb-3"
                            style={{
                              fontSize: "1rem",
                              lineHeight: "1.6",
                              color: "rgba(255, 255, 255, 0.85)",
                            }}
                          >
                            <i
                              className="fas fa-check-circle me-3 flex-shrink-0 mt-1"
                              style={{
                                fontSize: "1.1rem",
                                color: "#00a0e9",
                              }}
                            ></i>
                            <span style={{ textAlign: "justify" }}>{item}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </Col>
                <Col lg={6} md={12} className="d-flex justify-content-center">
                  <div
                    className="image-container"
                    style={{
                      ...featureImageContainerStyle(
                        hoveredImage === section.id
                      ),
                      maxWidth: "100%",
                      width: "100%",
                    }}
                    onMouseEnter={() => setHoveredImage(section.id)}
                    onMouseLeave={() => setHoveredImage(null)}
                  >
                    <Image
                      src={section.image}
                      alt={section.alt}
                      fluid
                      className="animate__animated animate__fadeInRight animate__delay-0.5s"
                      style={{
                        ...featureImageStyle,
                        transform:
                          hoveredImage === section.id
                            ? "scale(1.05)"
                            : "scale(1)",
                        height: "350px",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

      {/* Global CSS */}
      <style>{`
                :root {
                    --bs-primary: ${primaryColor};
                    --bs-primary-dark: ${primaryDarkColor};
                    --bs-primary-rgb: ${primaryRgb};
                }

                h1, h2, h3, h4, h5, h6 {
                    line-height: 1.2;
                }

                p {
                    line-height: 1.75;
                }

                .container {
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                }

                .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

                .content-wrapper {
                    padding: 0;
                }

                .image-container {
                    position: relative;
                    overflow: hidden;
                }

                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px) rotate(0deg);
                        opacity: 0.6;
                    }
                    33% {
                        transform: translateY(-15px) rotate(120deg);
                        opacity: 1;
                    }
                    66% {
                        transform: translateY(5px) rotate(240deg);
                        opacity: 0.8;
                    }
                }

                @keyframes pulse {
                    0%, 100% {
                        transform: scale(1);
                        opacity: 0.15;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.25;
                    }
                }

                @media (min-width: 768px) {
                    .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                    .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                    .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
                    .mb-md-5 { margin-bottom: 4rem !important; }
                    .mb-md-6 { margin-bottom: 6rem !important; }
                    .mb-md-8 { margin-bottom: 8rem !important; }
                }
            `}</style>
    </div>
  );
};

export default StaffAugmentationPage;
