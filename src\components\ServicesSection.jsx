import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ServiceCard from './ServiceCard';
import Home1 from "../Asserts/Home1.jpg";
import Home2 from "../Asserts/Home2.png";
import Home3 from "../Asserts/Home3.jpg";
import Home4 from "../Asserts/Home4.png";
import Home6 from "../Asserts/Home6.jpg";
import Home5 from "../Asserts/Home5.png";
gsap.registerPlugin(ScrollTrigger);

// Enhanced SVG Blob for a more modern feel - example
const ModernBlob1 = () => (
  <svg width="500" height="500" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.08, position: 'absolute', top: '-100px', right: '-150px' }}>
    <defs>
      <linearGradient id="blobGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#007bff', stopOpacity: 1 }} /> {/* Primary Blue */}
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} /> {/* Lighter Blue from your example */}
      </linearGradient>
    </defs>
    <path fill="url(#blobGradient1)" d="M60.1,-66.9C76.5,-56.8,87.5,-37.9,89.9,-18.3C92.3,1.3,86.1,21.5,74.1,37.9C62.1,54.3,44.3,67,25.5,73.7C6.7,80.4,-13.2,81.2,-30.9,74.8C-48.6,68.4,-64.1,54.8,-72.4,38.2C-80.7,21.6,-81.8,2,-76.5,-16.1C-71.2,-34.2,-59.5,-50.8,-44.4,-61C-29.3,-71.1,-10.8,-74.7,9.3,-77.2C29.4,-79.7,51.1,-82.3,60.1,-66.9Z" transform="translate(100 100) scale(1.1)" />
  </svg>
);

const ModernBlob2 = () => (
  <svg width="400" height="400" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.07, position: 'absolute', bottom: '-120px', left: '-100px' }}>
     <defs>
      <linearGradient id="blobGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#6f42c1', stopOpacity: 1 }} /> {/* A nice purple */}
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} /> {/* Lighter Blue */}
      </linearGradient>
    </defs>
    <path fill="url(#blobGradient2)" d="M50.9,-57.9C65.9,-47.7,78.1,-33.5,81.2,-17.2C84.3,-0.9,78.3,17.6,67.3,32.8C56.3,48,40.3,59.9,23.3,66.6C6.2,73.3,-11.9,74.8,-28.7,69.8C-45.5,64.8,-61,53.3,-69.5,38.3C-77.9,23.3,-79.3,4.8,-74.7,-12.6C-70.1,-30,-59.5,-46.3,-45.7,-56.6C-31.9,-66.9,-14.9,-71.2,2.4,-73.2C19.7,-75.2,35.9,-68.1,50.9,-57.9Z" transform="translate(100 100) scale(0.9)" />
  </svg>
);


const ServicesSection = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const cardsRef = useRef([]);
  const backgroundRef = useRef(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.from(titleRef.current, {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Cards stagger animation
      gsap.from(cardsRef.current, {
        y: 80,
        opacity: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 70%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Background parallax effect
      gsap.to(backgroundRef.current, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const services = [
    {
      id: 1,
      title: 'Artificial Intelligence',
      description: 'NLP services to extract insights from unstructured language and Computer Vision to recognize objects of interest.',
      image: Home1,
      icon: 'fa-brain', // Consider using a more modern icon pack or SVGs
      color: '#6495ED'
    },
    {
      id: 2,
      title: 'Internet of Things',
      description: 'As IoT advances, the dividing line between reality and virtual reality becomes blurred in a creative way.',
      image: Home2, // Example: code
      icon: 'fa-network-wired',
      color: '#6495ED' 
    },
    {
      id: 3,
      title: 'Analytics',
      description: 'When we have all data online it will be great for humanity. It helps solve many problems that humankind faces.',
      image: Home3,
      icon: 'fa-chart-pie',
      color: '#6495ED' 
    },
    {
      id: 4,
      title: 'Web & Mobile Development',
      description: 'Faster page optimization, smooth navigation and responsive page is not a magic, but the smart developers make it happen.',
      image: Home4,
      icon: 'fa-laptop-code',
      color: '#6495ED' 
    },
    {
      id: 5,
      title: 'Testing',
      description: 'Testing is an infinite process of comparing the invisible to the ambiguous in order to avoid the unthinkable happening to the anonymous.',
      image: Home5,
      icon: 'fa-vial',
      color: '#6495ED' 
    },
    {
      id: 6,
      title: 'Embedded Systems',
      description: 'Working on the integrated systems have been challenging so far, why not make it a worthwhile product eventually?',
      image:  Home6,
      icon: 'fa-microchip',
      color: '#6495ED' 
    }
  ];

  return (
    <section
      ref={sectionRef}
      className="section-padding relative overflow-hidden"
      id="services-section"
      style={{
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)',
        backdropFilter: 'blur(10px)'
      }}
    >
      {/* Enhanced Background Elements */}
      <div ref={backgroundRef} className="absolute inset-0 z-0">
        {/* Animated Grid Pattern */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
          }}
        />

        {/* Floating Tech Icons */}
        {[
          { lordicon: 'https://cdn.lordicon.com/qhviklyi.json', top: '10%', left: '5%', delay: 0 },
          { lordicon: 'https://cdn.lordicon.com/kiynvdns.json', top: '20%', right: '8%', delay: 1 },
          { lordicon: 'https://cdn.lordicon.com/hwjcdycb.json', bottom: '15%', left: '3%', delay: 2 },
          { lordicon: 'https://cdn.lordicon.com/qhgmphtg.json', bottom: '25%', right: '5%', delay: 3 }
        ].map((item, index) => (
          <div
            key={index}
            className="absolute w-15 h-15 bg-makonis-secondary/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-makonis-secondary/20 animate-float"
            style={{
              ...item,
              animationDelay: `${item.delay}s`
            }}
          >
            <lord-icon
              src={item.lordicon}
              trigger="hover"
              colors="primary:#00a0e9"
              style={{ width: '24px', height: '24px' }}>
            </lord-icon>
          </div>
        ))}

        <ModernBlob1 />
        <ModernBlob2 />
      </div>

      <div className="container-makonis relative z-10">
        <div ref={titleRef} className="text-center mb-16 lg:mb-24">
          

          <h2  style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>
            Innovative Solutions
          </h2>

          <p className="text-xl text-white/90 leading-relaxed mx-auto mb-4 max-w-3xl">
            Makonis Software provides a wide range of services to help businesses
            leverage technology for growth and innovation.
          </p>

          {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 justify-center">
          {services.map((service, index) => (
            <div
              key={service.id}
              className="flex"
              ref={el => cardsRef.current[index] = el}
            >
              <ServiceCard service={service} />
            </div>
          ))}
        </div>

        {/* Call to Action */}
       
      </div>


    </section>
  );
};

export default ServicesSection;